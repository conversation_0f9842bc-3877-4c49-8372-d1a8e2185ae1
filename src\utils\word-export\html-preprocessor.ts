/**
 * HTML预处理工具
 * 处理html-to-docx无法很好处理的HTML元素和样式
 */
import { JSDOM } from 'jsdom';

export interface PreprocessOptions {
  /** 是否处理着重号 */
  processEmphasisMarks?: boolean;
  /** 是否处理display:inline-block元素 */
  processInlineBlocks?: boolean;
  /** 是否处理浮动元素 */
  processFloatingElements?: boolean;
  /** 是否优化表格样式 */
  optimizeTableStyles?: boolean;
}

/**
 * HTML预处理器
 */
export class HtmlPreprocessor {
  private dom: JSDOM;
  private document: Document;

  constructor(htmlContent: string) {
    this.dom = new JSDOM(htmlContent);
    this.document = this.dom.window.document;
  }

  /**
   * 执行预处理
   */
  preprocess(options: PreprocessOptions = {}): string {
    const {
      processEmphasisMarks = true,
      processInlineBlocks = true,
      processFloatingElements = true,
      optimizeTableStyles = true,
    } = options;

    console.log('开始HTML预处理...');

    if (processEmphasisMarks) {
      this.processEmphasisMarks();
    }

    if (processInlineBlocks) {
      this.processInlineBlockElements();
    }

    if (processFloatingElements) {
      this.removeFloatingElements();
    }

    if (optimizeTableStyles) {
      this.optimizeTableStyles();
    }

    // 清理可能导致问题的样式
    this.cleanupProblematicStyles();

    console.log('HTML预处理完成');
    return this.getProcessedHtml();
  }

  /**
   * 处理着重号
   * 使用特殊标记方案：将着重号转换为特殊标记，供后续docx库处理
   */
  private processEmphasisMarks(): void {
    console.log('处理着重号：转换为特殊标记...');

    // 查找所有带有着重号样式的元素
    const emphasisElements = this.document.querySelectorAll(
      '[data-emphasis-mark="dot"], .emphasis-mark, [style*="text-emphasis"]'
    );

    let processedCount = 0;

    emphasisElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const textContent = htmlElement.textContent?.trim();

      if (textContent) {
        // 转换为特殊标记格式
        htmlElement.innerHTML = `<strong>[EMPHASIS]${textContent}[/EMPHASIS]</strong>`;

        // 移除原有的着重号相关样式和属性
        htmlElement.removeAttribute('data-emphasis-mark');
        htmlElement.classList.remove('emphasis-mark');
        htmlElement.style.removeProperty('text-emphasis');
        htmlElement.style.removeProperty('text-emphasis-position');
        htmlElement.style.removeProperty('text-decoration');
        htmlElement.style.removeProperty('border-bottom');

        processedCount++;
        console.log(`转换着重号为特殊标记: "${textContent}"`);
      }
    });

    console.log(`着重号预处理完成，转换了 ${processedCount} 个着重号为特殊标记`);
  }

  /**
   * 处理display:inline-block元素
   * 确保这些元素在Word中正确显示
   */
  private processInlineBlockElements(): void {
    console.log('处理inline-block元素...');

    // 首先处理inline-block元素
    const inlineBlockElements = this.document.querySelectorAll('[style*="display: inline-block"], [style*="display:inline-block"]');

    inlineBlockElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      if (style.display === 'inline-block') {
        console.log('处理inline-block元素:', htmlElement.tagName);

        // 检查元素是否包含块级内容
        const hasBlockContent = this.hasBlockLevelContent(htmlElement);

        if (hasBlockContent) {
          // 如果包含块级内容，需要特殊处理
          this.convertInlineBlockWithBlockContent(htmlElement);
        } else {
          // 如果只包含行内内容，转换为span以确保行内显示
          this.convertInlineBlockToSpan(htmlElement);
        }
      }
    });

    // 然后处理可能导致换行的div容器结构
    this.optimizeContainerStructure();
  }

  /**
   * 检查元素是否包含块级内容
   */
  private hasBlockLevelContent(element: HTMLElement): boolean {
    const blockTags = ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'table', 'tr', 'td', 'th'];

    for (const child of element.children) {
      if (blockTags.includes(child.tagName.toLowerCase())) {
        return true;
      }
    }
    return false;
  }

  /**
   * 转换包含块级内容的inline-block元素
   */
  private convertInlineBlockWithBlockContent(element: HTMLElement): void {
    console.log('转换包含块级内容的inline-block元素');

    // 对于包含块级内容的inline-block，保持其结构但优化样式
    // 移除可能导致换行的样式
    element.style.display = 'inline';

    // 确保内部的块级元素不会导致换行
    const blockChildren = element.querySelectorAll('div, p, h1, h2, h3, h4, h5, h6');
    blockChildren.forEach(child => {
      const childElement = child as HTMLElement;
      // 将内部块级元素转换为行内元素
      childElement.style.display = 'inline';
      childElement.style.margin = '0';
      childElement.style.padding = '0';
    });
  }

  /**
   * 转换只包含行内内容的inline-block元素为span
   */
  private convertInlineBlockToSpan(element: HTMLElement): void {
    console.log('转换行内inline-block元素为span');

    // 如果是div但只包含行内内容，转换为span
    if (element.tagName.toLowerCase() === 'div') {
      const span = this.document.createElement('span');

      // 复制所有属性
      for (let i = 0; i < element.attributes.length; i++) {
        const attr = element.attributes[i];
        span.setAttribute(attr.name, attr.value);
      }

      // 复制内容
      span.innerHTML = element.innerHTML;

      // 设置为行内显示
      span.style.display = 'inline';

      // 替换原元素
      element.parentNode?.replaceChild(span, element);
    } else {
      // 其他元素直接修改display
      element.style.display = 'inline';
    }
  }

  /**
   * 优化容器结构，减少不必要的div嵌套导致的换行
   */
  private optimizeContainerStructure(): void {
    console.log('优化容器结构...');

    // 第一步：处理题目和选项的容器结构
    const questionContainers = this.document.querySelectorAll('div[style*="align-items: center"]');

    questionContainers.forEach(container => {
      const htmlContainer = container as HTMLElement;

      // 检查是否是题目或选项容器
      if (this.isQuestionOrOptionContainer(htmlContainer)) {
        console.log('优化题目/选项容器结构');
        this.optimizeQuestionContainer(htmlContainer);
      }
    });

    // 第二步：处理选项组容器（没有特定样式的普通div）
    this.optimizeOptionGroupContainers();
  }

  /**
   * 判断是否是题目或选项容器
   */
  private isQuestionOrOptionContainer(container: HTMLElement): boolean {
    // 检查是否包含题目编号或选项标记
    const textContent = container.textContent || '';

    // 题目编号模式：数字 + 点 + 空格
    const questionPattern = /^\s*\d+\.\s/;

    // 选项标记模式：字母 + 、或点 + 空格
    const optionPattern = /^\s*[A-Z][、.]\s/;

    return questionPattern.test(textContent) || optionPattern.test(textContent);
  }

  /**
   * 优化题目容器结构
   */
  private optimizeQuestionContainer(container: HTMLElement): void {
    // 将容器转换为span，保持行内显示
    const span = this.document.createElement('span');

    // 复制所有属性，但修改display
    for (let i = 0; i < container.attributes.length; i++) {
      const attr = container.attributes[i];
      span.setAttribute(attr.name, attr.value);
    }

    // 设置为行内显示，保留其他样式
    span.style.display = 'inline';
    span.style.whiteSpace = 'nowrap'; // 保持不换行

    // 复制内容
    span.innerHTML = container.innerHTML;

    // 替换原容器
    container.parentNode?.replaceChild(span, container);

    // 在span后添加一个空格，确保与下一个元素有适当间距
    const space = this.document.createTextNode(' ');
    span.parentNode?.insertBefore(space, span.nextSibling);
  }

  /**
   * 优化选项组容器
   */
  private optimizeOptionGroupContainers(): void {
    console.log('优化选项组容器...');

    // 查找可能是选项组的div容器
    // 这些容器通常只包含选项，没有特殊的样式标识
    const allDivs = Array.from(this.document.querySelectorAll('div'));

    // 需要转换的div列表
    const divsToConvert: HTMLElement[] = [];

    allDivs.forEach(div => {
      const htmlDiv = div as HTMLElement;

      // 检查是否是选项组容器
      if (this.isOptionGroupContainer(htmlDiv)) {
        console.log('标记选项组容器待转换');
        divsToConvert.push(htmlDiv);
      }
    });

    // 批量转换div为span
    divsToConvert.forEach(div => {
      console.log('转换选项组容器div为span');
      this.convertDivToSpan(div);
    });
  }

  /**
   * 判断是否是选项组容器
   */
  private isOptionGroupContainer(div: HTMLElement): boolean {
    // 检查div是否主要包含选项元素
    const children = Array.from(div.children);

    // 如果div的直接子元素主要是span（已转换的选项容器），则认为是选项组
    const spanChildren = children.filter(child =>
      child.tagName.toLowerCase() === 'span' &&
      child.textContent &&
      /[A-Z][、.]/.test(child.textContent)
    );

    // 更宽松的检测条件：
    // 1. 如果有选项span，且选项span数量 >= 2，则认为是选项组
    // 2. 或者如果选项span占主要部分（>= 50%）
    const hasMultipleOptions = spanChildren.length >= 2;
    const optionsAreMajority = spanChildren.length > 0 && spanChildren.length >= children.length * 0.5;

    const isOptionGroup = hasMultipleOptions || optionsAreMajority;

    if (isOptionGroup) {
      console.log(`检测到选项组容器，包含 ${spanChildren.length} 个选项，共 ${children.length} 个子元素`);
      console.log(`容器内容预览: "${div.textContent?.substring(0, 100)}..."`);
    } else if (spanChildren.length > 0) {
      console.log(`跳过容器（选项不足），包含 ${spanChildren.length} 个选项，共 ${children.length} 个子元素`);
    }

    return isOptionGroup;
  }

  /**
   * 将div转换为span
   */
  private convertDivToSpan(div: HTMLElement): void {
    const span = this.document.createElement('span');

    // 复制所有属性
    for (let i = 0; i < div.attributes.length; i++) {
      const attr = div.attributes[i];
      span.setAttribute(attr.name, attr.value);
    }

    // 设置为行内显示
    span.style.display = 'inline';

    // 复制内容
    span.innerHTML = div.innerHTML;

    // 替换原div
    div.parentNode?.replaceChild(span, div);
  }

  /**
   * 移除浮动元素
   * position:absolute等浮动元素在Word转换中可能造成问题
   */
  private removeFloatingElements(): void {
    console.log('处理浮动元素...');

    const floatingElements = this.document.querySelectorAll('*');

    floatingElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      if (style.position === 'absolute' || style.position === 'fixed') {
        // 移除浮动定位，避免在Word转换中造成布局问题
        style.removeProperty('position');
        style.removeProperty('top');
        style.removeProperty('left');
        style.removeProperty('right');
        style.removeProperty('bottom');

        console.log('移除浮动定位样式');
      }
    });
  }

  /**
   * 优化表格样式
   * 彻底修复双线边框问题，确保单线边框
   */
  private optimizeTableStyles(): void {
    console.log('优化表格样式...');

    const tables = this.document.querySelectorAll('table');

    tables.forEach(table => {
      const htmlTable = table as HTMLTableElement;

      // 彻底清理表格样式，重新设置
      htmlTable.removeAttribute('border');
      htmlTable.removeAttribute('cellpadding');
      htmlTable.removeAttribute('cellspacing');

      // 关键设置：确保边框合并
      htmlTable.style.borderCollapse = 'collapse';
      htmlTable.style.borderSpacing = '0';

      // 移除表格本身的所有边框样式
      htmlTable.style.removeProperty('border');
      htmlTable.style.removeProperty('border-top');
      htmlTable.style.removeProperty('border-bottom');
      htmlTable.style.removeProperty('border-left');
      htmlTable.style.removeProperty('border-right');

      // 设置表格宽度
      if (!htmlTable.style.width) {
        htmlTable.style.width = '100%';
      }

      // 处理表格单元格，确保单线边框
      const cells = htmlTable.querySelectorAll('td, th');
      cells.forEach(cell => {
        const htmlCell = cell as HTMLTableCellElement;

        // 彻底清理单元格属性
        htmlCell.removeAttribute('width');
        htmlCell.removeAttribute('height');
        htmlCell.removeAttribute('border');
        htmlCell.removeAttribute('cellpadding');
        htmlCell.removeAttribute('cellspacing');

        // 清理所有边框样式
        htmlCell.style.removeProperty('border-top');
        htmlCell.style.removeProperty('border-bottom');
        htmlCell.style.removeProperty('border-left');
        htmlCell.style.removeProperty('border-right');
        htmlCell.style.removeProperty('border-width');
        htmlCell.style.removeProperty('border-style');
        htmlCell.style.removeProperty('border-color');

        // 重新设置单一边框 - 这是关键
        htmlCell.style.border = '1px solid black';

        // 设置内边距
        htmlCell.style.padding = '6px 8px';

        // 设置垂直对齐
        htmlCell.style.verticalAlign = 'top';

        // 确保文本换行正常
        htmlCell.style.overflowWrap = 'break-word';
      });
    });
  }

  /**
   * 清理可能导致问题的样式
   */
  private cleanupProblematicStyles(): void {
    console.log('清理问题样式...');

    const allElements = this.document.querySelectorAll('*');

    allElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      // 清理可能导致XML问题的样式属性
      const problematicProperties = [
        'white-space-collapse',
        'overflow-wrap',
        'word-break',
        'max-width',
      ];

      problematicProperties.forEach(prop => {
        if (style.getPropertyValue(prop)) {
          style.removeProperty(prop);
        }
      });

      // 清理可能有问题的类名
      if (htmlElement.className) {
        // 移除可能导致问题的类
        const problematicClasses = ['MsoNormal', 'double-underline'];
        problematicClasses.forEach(className => {
          htmlElement.classList.remove(className);
        });
      }

      // 清理align属性（使用style代替）
      if (htmlElement.hasAttribute('align')) {
        const alignValue = htmlElement.getAttribute('align');
        htmlElement.removeAttribute('align');
        if (alignValue && !style.textAlign) {
          style.textAlign = alignValue;
        }
      }

      // 特别处理表格单元格的width属性
      if (htmlElement.tagName === 'TD' || htmlElement.tagName === 'TH') {
        // 移除width属性，这可能导致XML问题
        htmlElement.removeAttribute('width');

        // 清理所有可能有问题的宽度样式
        if (style.width) {
          // 移除所有宽度样式，让表格自动调整
          style.removeProperty('width');
        }
      }

      // 特别处理表格的样式
      if (htmlElement.tagName === 'TABLE') {
        // 清理表格的高度和像素宽度
        if (style.height) {
          style.removeProperty('height');
        }
        if (style.width && style.width.includes('px')) {
          style.width = '100%';
        }
      }
    });
  }

  /**
   * 获取处理后的HTML
   */
  private getProcessedHtml(): string {
    return this.dom.serialize();
  }
}

/**
 * 预处理HTML内容
 * @param htmlContent 原始HTML内容
 * @param options 预处理选项
 * @returns 预处理后的HTML内容
 */
export function preprocessHtml(
  htmlContent: string,
  options: PreprocessOptions = {}
): string {
  const preprocessor = new HtmlPreprocessor(htmlContent);
  return preprocessor.preprocess(options);
}
