/**
 * Word文档XML补丁处理器 V2
 * 简洁设计：自动检测并处理html-to-docx无法处理的特殊样式
 * 可扩展设计：易于添加新的特殊样式处理
 */
import * as JSZip from 'jszip';
import { DOMParser, XMLSerializer } from 'xmldom';

export interface EmphasisTarget {
  text: string;
  shouldHaveEmphasis: boolean;
}

/**
 * Word文档XML补丁处理器
 * 职责：处理html-to-docx无法处理的特殊样式
 */
export class WordXmlPatcherV2 {
  private docxBuffer: Buffer;
  private originalHtml: string;

  constructor(docxBuffer: Buffer, originalHtml: string = '') {
    this.docxBuffer = docxBuffer;
    this.originalHtml = originalHtml;
  }

  /**
   * 应用补丁
   * 自动检测并处理html-to-docx无法处理的特殊样式
   */
  async applyPatches(): Promise<Buffer> {
    console.log('开始Word文档XML补丁处理...');

    // 自动检测需要处理的特殊样式
    const needsProcessing = this.detectSpecialStyles();

    if (!needsProcessing) {
      console.log('无需XML补丁处理，返回原始文档');
      return this.docxBuffer;
    }

    try {
      // 解析Word文档
      const zip = new JSZip();
      const docxZip = await zip.loadAsync(this.docxBuffer);

      // 读取document.xml
      const documentXml = await docxZip
        .file('word/document.xml')
        ?.async('text');
      if (!documentXml) {
        throw new Error('无法读取Word文档内容');
      }

      console.log('成功读取Word文档XML');

      // 解析XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(documentXml, 'text/xml');

      let modified = false;

      // 处理着重号（如果检测到）
      if (this.hasEmphasisMarks()) {
        const emphasisProcessed = await this.processEmphasisMarks(xmlDoc);
        if (emphasisProcessed) {
          modified = true;
          console.log('着重号处理完成');
        }
      }

      // 处理不必要的换行（如果检测到）
      if (this.hasUnnecessaryLineBreaks()) {
        const lineBreaksProcessed = await this.processUnnecessaryLineBreaks(
          xmlDoc
        );
        if (lineBreaksProcessed) {
          modified = true;
          console.log('换行优化处理完成');
        }
      }

      // 处理文档格式优化（行间距和对齐）
      if (this.needsDocumentFormatting()) {
        const formattingProcessed = await this.processDocumentFormatting(xmlDoc);
        if (formattingProcessed) {
          modified = true;
          console.log('文档格式优化处理完成');
        }
      }

      // 扩展点：在这里添加其他特殊样式处理
      // if (this.hasSpecialFonts()) {
      //   const fontsProcessed = await this.processSpecialFonts(xmlDoc);
      //   if (fontsProcessed) modified = true;
      // }
      // if (this.hasSpecialColors()) {
      //   const colorsProcessed = await this.processSpecialColors(xmlDoc);
      //   if (colorsProcessed) modified = true;
      // }

      if (modified) {
        // 序列化修改后的XML
        const serializer = new XMLSerializer();
        const modifiedXml = serializer.serializeToString(xmlDoc);

        // 更新ZIP中的document.xml
        docxZip.file('word/document.xml', modifiedXml);

        // 生成新的Word文档
        const patchedBuffer = await docxZip.generateAsync({
          type: 'nodebuffer',
        });

        console.log('Word文档XML补丁处理完成');
        return patchedBuffer;
      } else {
        console.log('无需修改，返回原始文档');
        return this.docxBuffer;
      }
    } catch (error) {
      console.error('XML补丁处理失败，回退到原始文档:', error);
      return this.docxBuffer;
    }
  }

  /**
   * 检测需要特殊处理的样式
   */
  private detectSpecialStyles(): boolean {
    const hasEmphasis = this.hasEmphasisMarks();
    const hasLineBreaks = this.hasUnnecessaryLineBreaks();
    // const hasSpecialFonts = this.hasSpecialFonts();
    // const hasSpecialColors = this.hasSpecialColors();

    if (hasEmphasis) {
      console.log('检测到着重号，需要XML补丁处理');
      return true;
    }

    if (hasLineBreaks) {
      console.log('检测到不必要的换行，需要XML补丁处理');
      return true;
    }

    const needsFormatting = this.needsDocumentFormatting();
    if (needsFormatting) {
      console.log('检测到需要文档格式优化，需要XML补丁处理');
      return true;
    }

    // 扩展点：添加其他特殊样式检测
    // if (hasSpecialFonts) {
    //   console.log('检测到特殊字体，需要XML补丁处理');
    //   return true;
    // }

    return false;
  }

  /**
   * 检查是否有着重号（特殊标记）
   */
  private hasEmphasisMarks(): boolean {
    return (
      this.originalHtml.includes('[EMPHASIS]') &&
      this.originalHtml.includes('[/EMPHASIS]')
    );
  }

  /**
   * 检查是否有不必要的换行
   * 通过检测HTML中的inline/inline-block元素来判断
   */
  private hasUnnecessaryLineBreaks(): boolean {
    // 检查是否包含inline或inline-block元素
    // 这些元素在HTML中应该是内联显示，但html-to-docx可能会将它们转换为段落
    const inlinePattern = /display:\s*(inline|inline-block)/g;
    const matches = this.originalHtml.match(inlinePattern);

    // 如果有inline/inline-block元素，需要优化换行
    const hasInlineElements = matches && matches.length > 0;

    if (hasInlineElements) {
      console.log(
        `检测到 ${matches.length} 个inline/inline-block元素，需要换行优化`
      );
    }

    return hasInlineElements;
  }

  /**
   * 检查是否需要文档格式优化
   * 包括行间距和文本对齐等
   */
  private needsDocumentFormatting(): boolean {
    // 检查是否包含居中对齐的内容
    const hasCenterAlignment = /text-align:\s*center/g.test(this.originalHtml);

    // 检查是否包含行间距设置
    const hasLineHeight = /line-height:\s*1\.5/g.test(this.originalHtml);

    // 如果有居中对齐或行间距设置，需要格式优化
    const needsFormatting = hasCenterAlignment || hasLineHeight;

    if (needsFormatting) {
      console.log('检测到需要格式优化的内容：', {
        hasCenterAlignment,
        hasLineHeight
      });
    }

    return needsFormatting;
  }

  /**
   * 处理着重号
   */
  private async processEmphasisMarks(xmlDoc: Document): Promise<boolean> {
    try {
      // 从HTML中提取需要着重号的文本
      const emphasisTargets = this.extractEmphasisTargets();

      if (emphasisTargets.length === 0) {
        console.log('未找到需要处理的着重号');
        return false;
      }

      console.log(`找到 ${emphasisTargets.length} 个需要着重号的文本`);

      // 在Word XML中查找并修改对应的文本节点
      let patchCount = 0;

      for (const target of emphasisTargets) {
        if (this.patchTextNodeEmphasis(xmlDoc, target.text)) {
          patchCount++;
        }
      }

      console.log(`成功应用 ${patchCount} 个着重号补丁`);
      return patchCount > 0;
    } catch (error) {
      console.error('着重号处理失败:', error);
      return false;
    }
  }

  /**
   * 处理不必要的换行
   * 基于inline/inline-block元素的通用处理方案
   */
  private async processUnnecessaryLineBreaks(
    xmlDoc: Document
  ): Promise<boolean> {
    try {
      console.log('开始处理不必要的换行...');
      console.log('策略：检测应该内联显示但被转换为段落的元素');

      // 查找所有段落
      const paragraphs = xmlDoc.getElementsByTagName('w:p');
      const paragraphArray = Array.from(paragraphs);

      console.log(`找到 ${paragraphArray.length} 个段落`);

      let processedCount = 0;

      // 查找应该内联显示的相邻段落
      for (let i = 0; i < paragraphArray.length - 1; i++) {
        const currentParagraph = paragraphArray[i];
        const nextParagraph = paragraphArray[i + 1];

        const currentText = this.getParagraphText(currentParagraph);
        const nextText = this.getParagraphText(nextParagraph);

        // 检查是否应该合并这两个段落
        const shouldMerge = this.shouldMergeParagraphs(
          currentParagraph,
          nextParagraph
        );

        if (shouldMerge) {
          console.log(`段落 ${i}: "${currentText.substring(0, 30)}..."`);
          console.log(`段落 ${i + 1}: "${nextText.substring(0, 30)}..."`);
          console.log('检测到应该内联显示的相邻段落，尝试合并');

          // 合并段落
          const mergeResult = this.mergeInlineParagraphs(
            xmlDoc,
            currentParagraph,
            nextParagraph
          );
          if (mergeResult) {
            processedCount++;
            // 跳过已处理的段落
            i++;
          }
        }
      }

      console.log(`成功处理 ${processedCount} 个换行优化`);
      return processedCount > 0;
    } catch (error) {
      console.error('换行处理失败:', error);
      return false;
    }
  }

  /**
   * 处理文档格式优化
   * 包括设置默认行间距为1.5和处理文本对齐
   */
  private async processDocumentFormatting(xmlDoc: Document): Promise<boolean> {
    try {
      console.log('开始处理文档格式优化...');

      let processedCount = 0;

      // 1. 设置默认行间距为1.5
      const lineSpacingProcessed = this.setDefaultLineSpacing(xmlDoc);
      if (lineSpacingProcessed) {
        processedCount++;
        console.log('默认行间距设置完成');
      }

      // 2. 处理文本对齐
      const alignmentProcessed = this.processTextAlignment(xmlDoc);
      if (alignmentProcessed) {
        processedCount++;
        console.log('文本对齐处理完成');
      }

      console.log(`成功处理 ${processedCount} 个格式优化`);
      return processedCount > 0;

    } catch (error) {
      console.error('文档格式处理失败:', error);
      return false;
    }
  }

  /**
   * 判断两个段落是否应该合并
   * 基于inline/inline-block元素的通用判断逻辑
   */
  private shouldMergeParagraphs(
    paragraph1: Element,
    paragraph2: Element
  ): boolean {
    try {
      const text1 = this.getParagraphText(paragraph1).trim();
      const text2 = this.getParagraphText(paragraph2).trim();

      // 跳过空段落
      if (!text1 || !text2) {
        return false;
      }

      // 策略1: 检测选项标签和内容的模式（保留原有逻辑）
      const isOptionLabelAndContent = this.isOptionLabelAndContentPair(
        text1,
        text2
      );
      if (isOptionLabelAndContent) {
        return true;
      }

      // 策略2: 检测题目序号和题目内容的组合 ⭐ 新增
      const isQuestionNumberAndContent = this.isQuestionNumberAndContent(
        text1,
        text2
      );
      if (isQuestionNumberAndContent) {
        return true;
      }

      // 策略3: 检测短文本段落（很可能来自inline元素）
      const isShortTextPair = this.isShortTextPair(text1, text2);
      if (isShortTextPair) {
        return true;
      }

      // 策略4: 检测特定的内联模式
      const isInlinePattern = this.isInlinePattern(text1, text2);
      if (isInlinePattern) {
        return true;
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检测选项标签和内容对
   */
  private isOptionLabelAndContentPair(text1: string, text2: string): boolean {
    // 第一个是选项标签：" A、 ", " B、 ", " C、 ", " D、 " 等
    const isLabel = /^\s*[A-Z][、.]\s*$/.test(text1);

    // 第二个是选项内容
    const isContent = text2.length > 0 && !/^\s*[A-Z][、.]\s*$/.test(text2);

    return isLabel && isContent;
  }

  /**
   * 检测短文本对（很可能来自inline元素）
   */
  private isShortTextPair(text1: string, text2: string): boolean {
    // 如果两个段落都很短（比如少于20个字符），很可能来自inline元素
    const isShort1 = text1.length <= 20;
    const isShort2 = text2.length <= 20;

    // 并且不是题目编号（避免误合并题目）
    const isNotQuestionNumber1 = !/^\s*\d+\.\s*$/.test(text1);
    const isNotQuestionNumber2 = !/^\s*\d+\.\s*$/.test(text2);

    return isShort1 && isShort2 && isNotQuestionNumber1 && isNotQuestionNumber2;
  }

  /**
   * 检测题目序号和题目内容的组合
   * 这是一个特殊情况：普通span元素（题目序号）+ inline-block div（题目内容）
   */
  private isQuestionNumberAndContent(text1: string, text2: string): boolean {
    // 第一个是题目序号：" 1. ", " 2. ", " 3. " 等
    const isQuestionNumber = /^\s*\d+\.\s*$/.test(text1);

    // 第二个是题目内容（通常比较长）
    const isQuestionContent = text2.length > 10 && !/^\s*\d+\.\s*$/.test(text2);

    return isQuestionNumber && isQuestionContent;
  }

  /**
   * 检测特定的内联模式
   */
  private isInlinePattern(text1: string, text2: string): boolean {
    // 检测一些常见的内联模式

    // 模式1: 标点符号 + 内容
    const isPunctuationAndContent =
      /^[，。、；：！？""''（）【】《》]+$/.test(text1) && text2.length > 0;

    // 模式2: 数字/字母 + 内容
    const isNumberAndContent =
      /^[A-Za-z0-9]+$/.test(text1) && text2.length > 0 && text2.length < 100;

    return isPunctuationAndContent || isNumberAndContent;
  }

  /**
   * 获取段落的文本内容
   */
  private getParagraphText(paragraph: Element): string {
    try {
      const textNodes = paragraph.getElementsByTagName('w:t');
      let text = '';
      for (let i = 0; i < textNodes.length; i++) {
        text += textNodes[i].textContent || '';
      }
      return text;
    } catch (error) {
      return '';
    }
  }

  /**
   * 合并内联段落
   * 通用的段落合并方法，适用于所有应该内联显示的元素
   */
  private mergeInlineParagraphs(
    xmlDoc: Document,
    paragraph1: Element,
    paragraph2: Element
  ): boolean {
    try {
      // 获取两个段落的文本
      const text1 = this.getParagraphText(paragraph1).trim();
      const text2 = this.getParagraphText(paragraph2).trim();

      console.log(`合并内联段落: "${text1}" + "${text2.substring(0, 30)}..."`);

      // 创建合并后的文本
      // 根据内容类型选择合适的分隔符
      const separator = this.chooseSeparator(text1, text2);
      const mergedText = `${text1}${separator}${text2}`;

      // 清空第一个段落的内容
      this.clearParagraphContent(paragraph1);

      // 创建新的运行节点包含合并后的文本
      const newRun = this.createSimpleTextRun(xmlDoc, mergedText);
      paragraph1.appendChild(newRun);

      // 删除第二个段落
      if (paragraph2.parentNode) {
        paragraph2.parentNode.removeChild(paragraph2);
      }

      console.log('成功合并内联段落');
      return true;
    } catch (error) {
      console.error('合并内联段落失败:', error);
      return false;
    }
  }

  /**
   * 根据内容类型选择合适的分隔符
   */
  private chooseSeparator(text1: string, text2: string): string {
    // 如果第一个文本以标点符号结尾，或第二个文本以标点符号开头，不需要分隔符
    if (
      /[，。、；：！？""''（）【】《》]$/.test(text1) ||
      /^[，。、；：！？""''（）【】《》]/.test(text2)
    ) {
      return '';
    }

    // 如果是选项标签（如 "A、"），用空格分隔
    if (/^\s*[A-Z][、.]\s*$/.test(text1)) {
      return ' ';
    }

    // 如果是数字或字母，用空格分隔
    if (/^[A-Za-z0-9]+$/.test(text1)) {
      return ' ';
    }

    // 默认用空格分隔
    return ' ';
  }

  /**
   * 清空段落内容，保留段落属性
   */
  private clearParagraphContent(paragraph: Element): void {
    const childNodes = Array.from(paragraph.childNodes);
    for (const child of childNodes) {
      if (child.nodeName !== 'w:pPr') {
        paragraph.removeChild(child);
      }
    }
  }

  /**
   * 创建简单的文本运行节点
   */
  private createSimpleTextRun(xmlDoc: Document, text: string): Element {
    const run = xmlDoc.createElement('w:r');
    const textNode = xmlDoc.createElement('w:t');
    textNode.textContent = text;

    // 设置xml:space属性以保留空格
    textNode.setAttribute('xml:space', 'preserve');

    run.appendChild(textNode);
    return run;
  }

  /**
   * 从HTML中提取需要着重号的文本
   */
  private extractEmphasisTargets(): EmphasisTarget[] {
    const targets: EmphasisTarget[] = [];

    try {
      // 使用正则表达式提取特殊标记中的文本
      const emphasisRegex = /\[EMPHASIS\](.*?)\[\/EMPHASIS\]/gi;
      let match: RegExpExecArray | null;

      while ((match = emphasisRegex.exec(this.originalHtml)) !== null) {
        const text = match[1].replace(/<[^>]*>/g, '').trim(); // 移除内部HTML标签
        if (text) {
          targets.push({
            text,
            shouldHaveEmphasis: true,
          });
        }
      }

      console.log(`从HTML中提取了 ${targets.length} 个着重号目标（特殊标记）`);
    } catch (error) {
      console.error('提取着重号目标失败:', error);
    }

    return targets;
  }

  /**
   * 在Word XML中为指定文本添加着重号样式
   */
  private patchTextNodeEmphasis(xmlDoc: Document, targetText: string): boolean {
    try {
      // 查找所有文本节点
      const textNodes = xmlDoc.getElementsByTagName('w:t');

      for (let i = 0; i < textNodes.length; i++) {
        const textNode = textNodes[i];
        const textContent = textNode.textContent || '';

        // 检查是否包含特殊标记
        const emphasisPattern = `[EMPHASIS]${targetText}[/EMPHASIS]`;
        if (textContent.includes(emphasisPattern)) {
          console.log(`找到特殊标记文本: "${emphasisPattern}"`);

          // 找到包含此文本节点的运行(w:r)和段落(w:p)
          const runNode = this.findParentRun(textNode);
          const paragraphNode = this.findParentParagraph(runNode);

          if (runNode && paragraphNode) {
            // 处理包含特殊标记的文本
            const processed = this.processEmphasisInText(
              xmlDoc,
              textContent,
              targetText,
              paragraphNode,
              runNode
            );
            if (processed) {
              console.log(`成功处理特殊标记: "${targetText}"`);
              return true;
            }
          }
        }
      }

      console.log(`未找到特殊标记: "[EMPHASIS]${targetText}[/EMPHASIS]"`);
      return false;
    } catch (error) {
      console.error(`为文本 "${targetText}" 添加着重号失败:`, error);
      return false;
    }
  }

  /**
   * 处理包含特殊标记的文本
   * 核心逻辑：分离普通文字和着重号文字，只给着重号文字添加样式
   */
  private processEmphasisInText(
    xmlDoc: Document,
    textContent: string,
    targetText: string,
    paragraphNode: Element,
    originalRunNode: Element
  ): boolean {
    try {
      // 使用正则表达式分割文本
      const emphasisPattern = `\\[EMPHASIS\\]${targetText.replace(
        /[.*+?^${}()|[\]\\]/g,
        '\\$&'
      )}\\[/EMPHASIS\\]`;
      const regex = new RegExp(emphasisPattern, 'g');

      // 分割文本
      const parts = textContent.split(regex);

      // 手动查找匹配项
      const matches: RegExpExecArray[] = [];
      let match: RegExpExecArray | null;
      const globalRegex = new RegExp(emphasisPattern, 'g');
      while ((match = globalRegex.exec(textContent)) !== null) {
        matches.push(match);
      }

      if (matches.length === 0) {
        return false;
      }

      // 获取原始运行的样式（但不包括可能的意外样式）
      const originalRPr = originalRunNode.getElementsByTagName('w:rPr')[0];

      // 创建新的运行节点来替换原始节点
      let partIndex = 0;
      for (let i = 0; i < parts.length; i++) {
        // 添加普通文本部分
        if (parts[i]) {
          const normalRun = this.createTextRun(
            xmlDoc,
            parts[i],
            false,
            originalRPr
          );
          paragraphNode.insertBefore(normalRun, originalRunNode);
        }

        // 添加着重号文本部分
        if (partIndex < matches.length) {
          const emphasisRun = this.createTextRun(
            xmlDoc,
            targetText,
            true,
            originalRPr
          );
          paragraphNode.insertBefore(emphasisRun, originalRunNode);
          partIndex++;
        }
      }

      // 移除原始运行节点
      paragraphNode.removeChild(originalRunNode);

      return true;
    } catch (error) {
      console.error('处理特殊标记文本失败:', error);
      return false;
    }
  }

  /**
   * 创建文本运行节点
   * 原则：其他样式不增也不减，只给着重号文字添加着重号样式
   */
  private createTextRun(
    xmlDoc: Document,
    text: string,
    isEmphasis: boolean,
    originalRPr?: Element
  ): Element {
    const runNode = xmlDoc.createElement('w:r');

    // 创建运行属性
    const rPrNode = xmlDoc.createElement('w:rPr');

    // 复制原始样式，但过滤掉可能导致问题的样式
    if (originalRPr) {
      const children = originalRPr.childNodes;
      for (let i = 0; i < children.length; i++) {
        const child = children[i];
        // 过滤掉可能是前面步骤导致的额外样式
        // 根据您的要求，如果是前面步骤导致的加粗、背景着色等，应该修改前面的步骤
        // 这里只保留必要的样式
        if (
          child.nodeName !== 'w:b' &&
          child.nodeName !== 'w:bCs' &&
          child.nodeName !== 'w:highlight' &&
          child.nodeName !== 'w:shd'
        ) {
          rPrNode.appendChild(child.cloneNode(true));
        }
      }
    }

    // 如果是着重号，只添加着重号样式
    if (isEmphasis) {
      const emphasisNode = xmlDoc.createElement('w:em');
      emphasisNode.setAttribute('w:val', 'dot');
      rPrNode.appendChild(emphasisNode);
    }

    runNode.appendChild(rPrNode);

    // 创建文本节点
    const textNode = xmlDoc.createElement('w:t');
    textNode.textContent = text;
    runNode.appendChild(textNode);

    return runNode;
  }

  /**
   * 查找文本节点的父运行节点
   */
  private findParentRun(textNode: Node): Element | null {
    let parent = textNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:r') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  /**
   * 查找运行节点的父段落节点
   */
  private findParentParagraph(runNode: Element | null): Element | null {
    if (!runNode) return null;

    let parent = runNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:p') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  /**
   * 设置默认行间距为1.5
   */
  private setDefaultLineSpacing(xmlDoc: Document): boolean {
    try {
      console.log('设置默认行间距为1.5...');

      // 查找所有段落
      const paragraphs = xmlDoc.getElementsByTagName('w:p');
      let processedCount = 0;

      for (let i = 0; i < paragraphs.length; i++) {
        const paragraph = paragraphs[i];

        // 获取或创建段落属性
        let pPr = paragraph.getElementsByTagName('w:pPr')[0];
        if (!pPr) {
          pPr = xmlDoc.createElement('w:pPr');
          paragraph.insertBefore(pPr, paragraph.firstChild);
        }

        // 检查是否已有行间距设置
        const existingSpacing = pPr.getElementsByTagName('w:spacing')[0];
        if (!existingSpacing) {
          // 创建行间距设置：1.5倍行距
          const spacing = xmlDoc.createElement('w:spacing');
          spacing.setAttribute('w:line', '360'); // 1.5 * 240 = 360 (240是单倍行距)
          spacing.setAttribute('w:lineRule', 'auto');
          pPr.appendChild(spacing);
          processedCount++;
        } else {
          // 检查现有行间距是否需要调整为1.5倍
          const currentLine = existingSpacing.getAttribute('w:line');
          if (currentLine !== '360') {
            existingSpacing.setAttribute('w:line', '360');
            existingSpacing.setAttribute('w:lineRule', 'auto');
            processedCount++;
          }
        }
      }

      console.log(`设置了 ${processedCount} 个段落的行间距`);
      return processedCount > 0;

    } catch (error) {
      console.error('设置行间距失败:', error);
      return false;
    }
  }

  /**
   * 处理文本对齐
   * 检测HTML中的text-align样式并应用到Word文档
   */
  private processTextAlignment(xmlDoc: Document): boolean {
    try {
      console.log('处理文本对齐...');

      // 查找所有段落
      const paragraphs = xmlDoc.getElementsByTagName('w:p');
      let processedCount = 0;

      for (let i = 0; i < paragraphs.length; i++) {
        const paragraph = paragraphs[i];
        const paragraphText = this.getParagraphText(paragraph);

        // 检查这个段落是否应该居中
        // 通过检查段落内容来判断是否是需要居中的内容
        if (this.shouldBeCentered(paragraphText)) {
          console.log(`设置段落居中: "${paragraphText.substring(0, 30)}..."`);

          // 获取或创建段落属性
          let pPr = paragraph.getElementsByTagName('w:pPr')[0];
          if (!pPr) {
            pPr = xmlDoc.createElement('w:pPr');
            paragraph.insertBefore(pPr, paragraph.firstChild);
          }

          // 检查是否已有对齐设置
          const existingJc = pPr.getElementsByTagName('w:jc')[0];
          if (!existingJc) {
            // 创建居中对齐设置
            const jc = xmlDoc.createElement('w:jc');
            jc.setAttribute('w:val', 'center');
            pPr.appendChild(jc);
            processedCount++;
          }
        }
      }

      console.log(`设置了 ${processedCount} 个段落的对齐方式`);
      return processedCount > 0;

    } catch (error) {
      console.error('处理文本对齐失败:', error);
      return false;
    }
  }

  /**
   * 判断段落是否应该居中
   * 基于内容特征来判断
   */
  private shouldBeCentered(text: string): boolean {
    // 检查是否是标题类内容（包含姓名、班级、考号等）
    const isHeaderInfo = /姓名|班级|考号|作业|考试|试卷/.test(text);

    // 检查是否是日期格式
    const isDate = /\d{4}年\d{1,2}月\d{1,2}日/.test(text);

    // 检查是否是标题格式（短文本且包含特定关键词）
    const isTitle = text.length < 50 && /阅读|访师|冒雪/.test(text);

    return isHeaderInfo || isDate || isTitle;
  }

  // 扩展点：添加其他特殊样式处理方法
  // private hasSpecialFonts(): boolean { return false; }
  // private processSpecialFonts(xmlDoc: Document): Promise<boolean> { return Promise.resolve(false); }
  // private hasSpecialColors(): boolean { return false; }
  // private processSpecialColors(xmlDoc: Document): Promise<boolean> { return Promise.resolve(false); }
}

/**
 * 便捷函数：对Word文档应用XML补丁
 * 简洁接口：自动检测并处理特殊样式
 */
export async function patchWordDocument(
  docxBuffer: Buffer,
  originalHtml: string
): Promise<Buffer> {
  const patcher = new WordXmlPatcherV2(docxBuffer, originalHtml);
  return await patcher.applyPatches();
}
