/**
 * Word文档XML补丁处理器 V2
 * 简洁设计：自动检测并处理html-to-docx无法处理的特殊样式
 * 可扩展设计：易于添加新的特殊样式处理
 */
import * as JSZip from 'jszip';
import { DOMParser, XMLSerializer } from 'xmldom';

export interface EmphasisTarget {
  text: string;
  shouldHaveEmphasis: boolean;
}

/**
 * Word文档XML补丁处理器
 * 职责：处理html-to-docx无法处理的特殊样式
 */
export class WordXmlPatcherV2 {
  private docxBuffer: Buffer;
  private originalHtml: string;

  constructor(docxBuffer: Buffer, originalHtml: string = '') {
    this.docxBuffer = docxBuffer;
    this.originalHtml = originalHtml;
  }

  /**
   * 应用补丁
   * 自动检测并处理html-to-docx无法处理的特殊样式
   */
  async applyPatches(): Promise<Buffer> {
    console.log('开始Word文档XML补丁处理...');

    // 自动检测需要处理的特殊样式
    const needsProcessing = this.detectSpecialStyles();

    if (!needsProcessing) {
      console.log('无需XML补丁处理，返回原始文档');
      return this.docxBuffer;
    }

    try {
      // 解析Word文档
      const zip = new JSZip();
      const docxZip = await zip.loadAsync(this.docxBuffer);

      // 读取document.xml
      const documentXml = await docxZip.file('word/document.xml')?.async('text');
      if (!documentXml) {
        throw new Error('无法读取Word文档内容');
      }

      console.log('成功读取Word文档XML');

      // 解析XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(documentXml, 'text/xml');

      let modified = false;

      // 处理着重号（如果检测到）
      if (this.hasEmphasisMarks()) {
        const emphasisProcessed = await this.processEmphasisMarks(xmlDoc);
        if (emphasisProcessed) {
          modified = true;
          console.log('着重号处理完成');
        }
      }

      // 扩展点：在这里添加其他特殊样式处理
      // if (this.hasSpecialFonts()) {
      //   const fontsProcessed = await this.processSpecialFonts(xmlDoc);
      //   if (fontsProcessed) modified = true;
      // }
      // if (this.hasSpecialColors()) {
      //   const colorsProcessed = await this.processSpecialColors(xmlDoc);
      //   if (colorsProcessed) modified = true;
      // }

      if (modified) {
        // 序列化修改后的XML
        const serializer = new XMLSerializer();
        const modifiedXml = serializer.serializeToString(xmlDoc);

        // 更新ZIP中的document.xml
        docxZip.file('word/document.xml', modifiedXml);

        // 生成新的Word文档
        const patchedBuffer = await docxZip.generateAsync({ type: 'nodebuffer' });

        console.log('Word文档XML补丁处理完成');
        return patchedBuffer;
      } else {
        console.log('无需修改，返回原始文档');
        return this.docxBuffer;
      }

    } catch (error) {
      console.error('XML补丁处理失败，回退到原始文档:', error);
      return this.docxBuffer;
    }
  }

  /**
   * 检测需要特殊处理的样式
   */
  private detectSpecialStyles(): boolean {
    const hasEmphasis = this.hasEmphasisMarks();
    // const hasSpecialFonts = this.hasSpecialFonts();
    // const hasSpecialColors = this.hasSpecialColors();

    if (hasEmphasis) {
      console.log('检测到着重号，需要XML补丁处理');
      return true;
    }

    // 扩展点：添加其他特殊样式检测
    // if (hasSpecialFonts) {
    //   console.log('检测到特殊字体，需要XML补丁处理');
    //   return true;
    // }

    return false;
  }

  /**
   * 检查是否有着重号（特殊标记）
   */
  private hasEmphasisMarks(): boolean {
    return this.originalHtml.includes('[EMPHASIS]') && 
           this.originalHtml.includes('[/EMPHASIS]');
  }

  /**
   * 处理着重号
   */
  private async processEmphasisMarks(xmlDoc: Document): Promise<boolean> {
    try {
      // 从HTML中提取需要着重号的文本
      const emphasisTargets = this.extractEmphasisTargets();
      
      if (emphasisTargets.length === 0) {
        console.log('未找到需要处理的着重号');
        return false;
      }

      console.log(`找到 ${emphasisTargets.length} 个需要着重号的文本`);

      // 在Word XML中查找并修改对应的文本节点
      let patchCount = 0;
      
      for (const target of emphasisTargets) {
        if (this.patchTextNodeEmphasis(xmlDoc, target.text)) {
          patchCount++;
        }
      }

      console.log(`成功应用 ${patchCount} 个着重号补丁`);
      return patchCount > 0;

    } catch (error) {
      console.error('着重号处理失败:', error);
      return false;
    }
  }

  /**
   * 从HTML中提取需要着重号的文本
   */
  private extractEmphasisTargets(): EmphasisTarget[] {
    const targets: EmphasisTarget[] = [];
    
    try {
      // 使用正则表达式提取特殊标记中的文本
      const emphasisRegex = /\[EMPHASIS\](.*?)\[\/EMPHASIS\]/gi;
      let match: RegExpExecArray | null;
      
      while ((match = emphasisRegex.exec(this.originalHtml)) !== null) {
        const text = match[1].replace(/<[^>]*>/g, '').trim(); // 移除内部HTML标签
        if (text) {
          targets.push({
            text,
            shouldHaveEmphasis: true
          });
        }
      }

      console.log(`从HTML中提取了 ${targets.length} 个着重号目标（特殊标记）`);
    } catch (error) {
      console.error('提取着重号目标失败:', error);
    }

    return targets;
  }

  /**
   * 在Word XML中为指定文本添加着重号样式
   */
  private patchTextNodeEmphasis(xmlDoc: Document, targetText: string): boolean {
    try {
      // 查找所有文本节点
      const textNodes = xmlDoc.getElementsByTagName('w:t');
      
      for (let i = 0; i < textNodes.length; i++) {
        const textNode = textNodes[i];
        const textContent = textNode.textContent || '';
        
        // 检查是否包含特殊标记
        const emphasisPattern = `[EMPHASIS]${targetText}[/EMPHASIS]`;
        if (textContent.includes(emphasisPattern)) {
          console.log(`找到特殊标记文本: "${emphasisPattern}"`);
          
          // 找到包含此文本节点的运行(w:r)和段落(w:p)
          const runNode = this.findParentRun(textNode);
          const paragraphNode = this.findParentParagraph(runNode);
          
          if (runNode && paragraphNode) {
            // 处理包含特殊标记的文本
            const processed = this.processEmphasisInText(xmlDoc, textContent, targetText, paragraphNode, runNode);
            if (processed) {
              console.log(`成功处理特殊标记: "${targetText}"`);
              return true;
            }
          }
        }
      }
      
      console.log(`未找到特殊标记: "[EMPHASIS]${targetText}[/EMPHASIS]"`);
      return false;
    } catch (error) {
      console.error(`为文本 "${targetText}" 添加着重号失败:`, error);
      return false;
    }
  }

  /**
   * 处理包含特殊标记的文本
   * 核心逻辑：分离普通文字和着重号文字，只给着重号文字添加样式
   */
  private processEmphasisInText(
    xmlDoc: Document, 
    textContent: string, 
    targetText: string, 
    paragraphNode: Element, 
    originalRunNode: Element
  ): boolean {
    try {
      // 使用正则表达式分割文本
      const emphasisPattern = `\\[EMPHASIS\\]${targetText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\[/EMPHASIS\\]`;
      const regex = new RegExp(emphasisPattern, 'g');
      
      // 分割文本
      const parts = textContent.split(regex);
      
      // 手动查找匹配项
      const matches: RegExpExecArray[] = [];
      let match: RegExpExecArray | null;
      const globalRegex = new RegExp(emphasisPattern, 'g');
      while ((match = globalRegex.exec(textContent)) !== null) {
        matches.push(match);
      }
      
      if (matches.length === 0) {
        return false;
      }
      
      // 获取原始运行的样式（但不包括可能的意外样式）
      const originalRPr = originalRunNode.getElementsByTagName('w:rPr')[0];
      
      // 创建新的运行节点来替换原始节点
      let partIndex = 0;
      for (let i = 0; i < parts.length; i++) {
        // 添加普通文本部分
        if (parts[i]) {
          const normalRun = this.createTextRun(xmlDoc, parts[i], false, originalRPr);
          paragraphNode.insertBefore(normalRun, originalRunNode);
        }
        
        // 添加着重号文本部分
        if (partIndex < matches.length) {
          const emphasisRun = this.createTextRun(xmlDoc, targetText, true, originalRPr);
          paragraphNode.insertBefore(emphasisRun, originalRunNode);
          partIndex++;
        }
      }
      
      // 移除原始运行节点
      paragraphNode.removeChild(originalRunNode);
      
      return true;
    } catch (error) {
      console.error('处理特殊标记文本失败:', error);
      return false;
    }
  }

  /**
   * 创建文本运行节点
   * 原则：其他样式不增也不减，只给着重号文字添加着重号样式
   */
  private createTextRun(xmlDoc: Document, text: string, isEmphasis: boolean, originalRPr?: Element): Element {
    const runNode = xmlDoc.createElement('w:r');
    
    // 创建运行属性
    const rPrNode = xmlDoc.createElement('w:rPr');
    
    // 复制原始样式，但过滤掉可能导致问题的样式
    if (originalRPr) {
      const children = originalRPr.childNodes;
      for (let i = 0; i < children.length; i++) {
        const child = children[i];
        // 过滤掉可能是前面步骤导致的额外样式
        // 根据您的要求，如果是前面步骤导致的加粗、背景着色等，应该修改前面的步骤
        // 这里只保留必要的样式
        if (child.nodeName !== 'w:b' && child.nodeName !== 'w:bCs' && 
            child.nodeName !== 'w:highlight' && child.nodeName !== 'w:shd') {
          rPrNode.appendChild(child.cloneNode(true));
        }
      }
    }
    
    // 如果是着重号，只添加着重号样式
    if (isEmphasis) {
      const emphasisNode = xmlDoc.createElement('w:em');
      emphasisNode.setAttribute('w:val', 'dot');
      rPrNode.appendChild(emphasisNode);
    }
    
    runNode.appendChild(rPrNode);
    
    // 创建文本节点
    const textNode = xmlDoc.createElement('w:t');
    textNode.textContent = text;
    runNode.appendChild(textNode);
    
    return runNode;
  }

  /**
   * 查找文本节点的父运行节点
   */
  private findParentRun(textNode: Node): Element | null {
    let parent = textNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:r') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  /**
   * 查找运行节点的父段落节点
   */
  private findParentParagraph(runNode: Element | null): Element | null {
    if (!runNode) return null;
    
    let parent = runNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:p') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  // 扩展点：添加其他特殊样式处理方法
  // private hasSpecialFonts(): boolean { return false; }
  // private processSpecialFonts(xmlDoc: Document): Promise<boolean> { return Promise.resolve(false); }
  // private hasSpecialColors(): boolean { return false; }
  // private processSpecialColors(xmlDoc: Document): Promise<boolean> { return Promise.resolve(false); }
}

/**
 * 便捷函数：对Word文档应用XML补丁
 * 简洁接口：自动检测并处理特殊样式
 */
export async function patchWordDocument(
  docxBuffer: Buffer,
  originalHtml: string
): Promise<Buffer> {
  const patcher = new WordXmlPatcherV2(docxBuffer, originalHtml);
  return await patcher.applyPatches();
}
