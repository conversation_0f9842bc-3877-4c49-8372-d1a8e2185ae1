/**
 * Word文档XML补丁处理器 V2
 * 简洁设计：自动检测并处理html-to-docx无法处理的特殊样式
 * 可扩展设计：易于添加新的特殊样式处理
 */
import * as JSZip from 'jszip';
import { DOMParser, XMLSerializer } from 'xmldom';

export interface EmphasisTarget {
  text: string;
  shouldHaveEmphasis: boolean;
}

/**
 * Word文档XML补丁处理器
 * 职责：处理html-to-docx无法处理的特殊样式
 */
export class WordXmlPatcherV2 {
  private docxBuffer: Buffer;
  private originalHtml: string;

  constructor(docxBuffer: Buffer, originalHtml: string = '') {
    this.docxBuffer = docxBuffer;
    this.originalHtml = originalHtml;
  }

  /**
   * 应用补丁
   * 自动检测并处理html-to-docx无法处理的特殊样式
   */
  async applyPatches(): Promise<Buffer> {
    console.log('开始Word文档XML补丁处理...');

    // 自动检测需要处理的特殊样式
    const needsProcessing = this.detectSpecialStyles();

    if (!needsProcessing) {
      console.log('无需XML补丁处理，返回原始文档');
      return this.docxBuffer;
    }

    try {
      // 解析Word文档
      const zip = new JSZip();
      const docxZip = await zip.loadAsync(this.docxBuffer);

      // 读取document.xml
      const documentXml = await docxZip
        .file('word/document.xml')
        ?.async('text');
      if (!documentXml) {
        throw new Error('无法读取Word文档内容');
      }

      console.log('成功读取Word文档XML');

      // 解析XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(documentXml, 'text/xml');

      let modified = false;

      // 处理着重号（如果检测到）
      if (this.hasEmphasisMarks()) {
        const emphasisProcessed = await this.processEmphasisMarks(xmlDoc);
        if (emphasisProcessed) {
          modified = true;
          console.log('着重号处理完成');
        }
      }

      // 扩展点：在这里添加其他特殊样式处理
      // 处理不必要的换行（如果检测到）
      if (this.hasUnnecessaryLineBreaks()) {
        const lineBreaksProcessed = await this.processUnnecessaryLineBreaks(xmlDoc);
        if (lineBreaksProcessed) {
          modified = true;
          console.log('换行优化处理完成');
        }
      }

      // // 处理文档格式优化（行间距和对齐）
      // if (this.needsDocumentFormatting()) {
      //   const formattingProcessed = await this.processDocumentFormatting(xmlDoc);
      //   if (formattingProcessed) {
      //     modified = true;
      //     console.log('文档格式优化处理完成');
      //   }
      // }

      // if (this.hasSpecialFonts()) {
      //   const fontsProcessed = await this.processSpecialFonts(xmlDoc);
      //   if (fontsProcessed) modified = true;
      // }
      // if (this.hasSpecialColors()) {
      //   const colorsProcessed = await this.processSpecialColors(xmlDoc);
      //   if (colorsProcessed) modified = true;
      // }

      if (modified) {
        // 序列化修改后的XML
        const serializer = new XMLSerializer();
        const modifiedXml = serializer.serializeToString(xmlDoc);

        // 更新ZIP中的document.xml
        docxZip.file('word/document.xml', modifiedXml);

        // 生成新的Word文档
        const patchedBuffer = await docxZip.generateAsync({
          type: 'nodebuffer',
        });

        console.log('Word文档XML补丁处理完成');
        return patchedBuffer;
      } else {
        console.log('无需修改，返回原始文档');
        return this.docxBuffer;
      }
    } catch (error) {
      console.error('XML补丁处理失败，回退到原始文档:', error);
      return this.docxBuffer;
    }
  }

  /**
   * 检测需要特殊处理的样式
   */
  private detectSpecialStyles(): boolean {
    const hasEmphasis = this.hasEmphasisMarks();
    const hasLineBreaks = this.hasUnnecessaryLineBreaks();
    const needsFormatting = this.needsDocumentFormatting();

    if (hasEmphasis) {
      console.log('检测到着重号，需要XML补丁处理');
      return true;
    }

    if (hasLineBreaks) {
      console.log('检测到不必要的换行，需要XML补丁处理');
      return true;
    }

    if (needsFormatting) {
      console.log('检测到需要文档格式优化，需要XML补丁处理');
      return true;
    }

    // 扩展点：添加其他特殊样式检测
    // if (hasSpecialFonts) {
    //   console.log('检测到特殊字体，需要XML补丁处理');
    //   return true;
    // }

    return false;
  }

  /**
   * 检测是否有着重号需要处理
   */
  private hasEmphasisMarks(): boolean {
    // 检查HTML中是否包含着重号的特殊标记
    const hasEmphasisMarks = /\[EMPHASIS\].*?\[\/EMPHASIS\]/g.test(this.originalHtml);
    
    if (hasEmphasisMarks) {
      console.log('从HTML中提取了', (this.originalHtml.match(/\[EMPHASIS\].*?\[\/EMPHASIS\]/g) || []).length, '个着重号目标（特殊标记）');
    }
    
    return hasEmphasisMarks;
  }

  /**
   * 处理着重号
   */
  private async processEmphasisMarks(xmlDoc: Document): Promise<boolean> {
    try {
      // 从HTML中提取需要着重号的文本
      const emphasisTargets = this.extractEmphasisTargets();

      if (emphasisTargets.length === 0) {
        console.log('未找到需要处理的着重号');
        return false;
      }

      console.log(`找到 ${emphasisTargets.length} 个需要着重号的文本`);

      // 在Word XML中查找并修改对应的文本节点
      let patchCount = 0;

      for (const target of emphasisTargets) {
        if (this.patchTextNodeEmphasis(xmlDoc, target.text)) {
          patchCount++;
        }
      }

      console.log(`成功应用 ${patchCount} 个着重号补丁`);
      return patchCount > 0;
    } catch (error) {
      console.error('着重号处理失败:', error);
      return false;
    }
  }

  /**
   * 从HTML中提取需要着重号的文本
   */
  private extractEmphasisTargets(): EmphasisTarget[] {
    const targets: EmphasisTarget[] = [];
    
    // 使用正则表达式提取所有着重号标记
    const emphasisPattern = /\[EMPHASIS\](.*?)\[\/EMPHASIS\]/g;
    let match: RegExpExecArray | null;
    
    while ((match = emphasisPattern.exec(this.originalHtml)) !== null) {
      const text = match[1];
      if (text && text.trim()) {
        targets.push({
          text: text.trim(),
          shouldHaveEmphasis: true
        });
      }
    }
    
    return targets;
  }

  /**
   * 在Word XML中查找并修改文本节点，添加着重号
   */
  private patchTextNodeEmphasis(xmlDoc: Document, targetText: string): boolean {
    try {
      // 查找所有文本节点
      const textNodes = xmlDoc.getElementsByTagName('w:t');

      for (let i = 0; i < textNodes.length; i++) {
        const textNode = textNodes[i];
        const textContent = textNode.textContent || '';

        // 检查是否包含特殊标记
        const emphasisPattern = `[EMPHASIS]${targetText}[/EMPHASIS]`;
        if (textContent.includes(emphasisPattern)) {
          console.log(`找到特殊标记文本: "${emphasisPattern}"`);

          // 找到包含此文本节点的运行(w:r)和段落(w:p)
          const runNode = this.findParentRun(textNode);
          const paragraphNode = this.findParentParagraph(runNode);

          if (runNode && paragraphNode) {
            // 处理包含特殊标记的文本
            const processed = this.processEmphasisInText(
              xmlDoc,
              textContent,
              targetText,
              paragraphNode,
              runNode
            );
            if (processed) {
              console.log(`成功处理特殊标记: "${targetText}"`);
              return true;
            }
          }
        }
      }

      return false;
    } catch (error) {
      console.error(`处理着重号文本失败: ${targetText}`, error);
      return false;
    }
  }

  /**
   * 处理包含特殊标记的文本，将其分割并添加着重号
   */
  private processEmphasisInText(
    xmlDoc: Document,
    fullText: string,
    targetText: string,
    paragraphNode: Element,
    originalRunNode: Element
  ): boolean {
    try {
      // 构建特殊标记模式
      const emphasisPattern = `[EMPHASIS]${targetText}[/EMPHASIS]`;
      
      // 分割文本
      const parts = fullText.split(emphasisPattern);
      const matches = fullText.match(new RegExp(emphasisPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || [];

      if (matches.length === 0) {
        return false;
      }

      // 获取原始运行的样式（但不包括可能的意外样式）
      const originalRPr = originalRunNode.getElementsByTagName('w:rPr')[0];

      // 创建新的运行节点来替换原始节点
      let partIndex = 0;
      for (let i = 0; i < parts.length; i++) {
        // 添加普通文本部分
        if (parts[i]) {
          const normalRun = this.createTextRun(
            xmlDoc,
            parts[i],
            false,
            originalRPr
          );
          paragraphNode.insertBefore(normalRun, originalRunNode);
        }

        // 添加着重号文本部分
        if (partIndex < matches.length) {
          const emphasisRun = this.createTextRun(
            xmlDoc,
            targetText,
            true,
            originalRPr
          );
          paragraphNode.insertBefore(emphasisRun, originalRunNode);
          partIndex++;
        }
      }

      // 移除原始运行节点
      paragraphNode.removeChild(originalRunNode);

      return true;
    } catch (error) {
      console.error('处理特殊标记文本失败:', error);
      return false;
    }
  }

  /**
   * 创建文本运行节点
   */
  private createTextRun(
    xmlDoc: Document,
    text: string,
    isEmphasis: boolean,
    originalRPr?: Element | null
  ): Element {
    const run = xmlDoc.createElement('w:r');
    
    // 创建运行属性
    const rPrNode = xmlDoc.createElement('w:rPr');
    
    // 如果是着重号，只添加着重号样式
    if (isEmphasis) {
      const emphasisNode = xmlDoc.createElement('w:em');
      emphasisNode.setAttribute('w:val', 'dot');
      rPrNode.appendChild(emphasisNode);
    }
    
    run.appendChild(rPrNode);
    
    // 创建文本节点
    const textNode = xmlDoc.createElement('w:t');
    textNode.textContent = text;
    run.appendChild(textNode);
    
    return run;
  }

  /**
   * 查找文本节点的父运行节点
   */
  private findParentRun(textNode: Node): Element | null {
    let parent = textNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:r') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  /**
   * 查找运行节点的父段落节点
   */
  private findParentParagraph(runNode: Element | null): Element | null {
    if (!runNode) return null;
    
    let parent = runNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:p') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  /**
   * 检测是否有不必要的换行需要处理
   */
  private hasUnnecessaryLineBreaks(): boolean {
    // 检查HTML中是否包含inline-block元素
    const hasInlineBlock = /display:\s*inline-block/g.test(this.originalHtml);

    // 检查是否包含选项标签模式
    const hasOptionPattern = /[A-Z][、.]\s*/.test(this.originalHtml);

    // 检查是否包含题目序号模式
    const hasNumberPattern = /\d+[、.]\s*/.test(this.originalHtml);

    const hasInlineElements = hasInlineBlock || hasOptionPattern || hasNumberPattern;

    if (hasInlineElements) {
      console.log('检测到内联元素，可能需要换行优化');
    }

    return hasInlineElements;
  }

  /**
   * 处理不必要的换行
   * 基于inline/inline-block元素的通用处理方案
   */
  private async processUnnecessaryLineBreaks(
    xmlDoc: Document
  ): Promise<boolean> {
    try {
      console.log('开始处理不必要的换行...');

      // 查找所有段落
      const paragraphs = xmlDoc.getElementsByTagName('w:p');
      let processedCount = 0;

      // 遍历段落，查找需要合并的相邻段落
      for (let i = 0; i < paragraphs.length - 1; i++) {
        const currentParagraph = paragraphs[i];
        const nextParagraph = paragraphs[i + 1];

        // 检查是否应该合并这两个段落
        if (this.shouldMergeParagraphs(currentParagraph, nextParagraph)) {
          // 合并段落
          this.mergeParagraphs(xmlDoc, currentParagraph, nextParagraph);
          processedCount++;

          // 由于合并了段落，需要重新开始遍历
          i = -1; // 下次循环会变成0
        }
      }

      console.log(`成功合并 ${processedCount} 个内联段落`);
      return processedCount > 0;

    } catch (error) {
      console.error('换行处理失败:', error);
      return false;
    }
  }

  /**
   * 检查是否需要文档格式优化
   * 包括行间距和文本对齐等
   */
  private needsDocumentFormatting(): boolean {
    // 检查是否包含居中对齐的内容
    const hasCenterAlignment = /text-align:\s*center/g.test(this.originalHtml);

    // 检查是否包含行间距设置
    const hasLineHeight = /line-height:\s*1\.5/g.test(this.originalHtml);

    // 如果有居中对齐或行间距设置，需要格式优化
    const needsFormatting = hasCenterAlignment || hasLineHeight;

    if (needsFormatting) {
      console.log('检测到需要格式优化的内容：', {
        hasCenterAlignment,
        hasLineHeight
      });
    }

    return needsFormatting;
  }

  /**
   * 判断两个段落是否应该合并
   * 基于inline/inline-block元素的通用判断逻辑
   */
  private shouldMergeParagraphs(
    currentParagraph: Element,
    nextParagraph: Element
  ): boolean {
    const currentText = this.getParagraphText(currentParagraph);
    const nextText = this.getParagraphText(nextParagraph);

    // 检查是否是选项标签和内容对
    if (this.isOptionLabelAndContentPair(currentText, nextText)) {
      return true;
    }

    // 检查是否是题目序号和内容对
    if (this.isQuestionNumberAndContentPair(currentText, nextText)) {
      return true;
    }

    // 检查是否都是短文本（可能需要内联显示）
    if (this.areShortTextPair(currentText, nextText)) {
      return true;
    }

    return false;
  }

  /**
   * 合并两个段落
   */
  private mergeParagraphs(
    xmlDoc: Document,
    currentParagraph: Element,
    nextParagraph: Element
  ): void {
    // 将下一个段落的所有运行节点移动到当前段落
    const nextRuns = nextParagraph.getElementsByTagName('w:r');

    // 在合并前添加一个空格分隔
    const spaceRun = this.createSimpleTextRun(xmlDoc, ' ');
    currentParagraph.appendChild(spaceRun);

    // 复制所有运行节点
    const runsToMove = Array.from(nextRuns);
    for (const run of runsToMove) {
      currentParagraph.appendChild(run.cloneNode(true));
    }

    // 删除下一个段落
    const parent = nextParagraph.parentNode;
    if (parent) {
      parent.removeChild(nextParagraph);
    }
  }

  /**
   * 创建简单的文本运行节点
   */
  private createSimpleTextRun(xmlDoc: Document, text: string): Element {
    const run = xmlDoc.createElement('w:r');
    const textNode = xmlDoc.createElement('w:t');
    textNode.textContent = text;
    run.appendChild(textNode);
    return run;
  }

  /**
   * 获取段落的文本内容
   */
  private getParagraphText(paragraph: Element): string {
    const textNodes = paragraph.getElementsByTagName('w:t');
    let text = '';
    for (let i = 0; i < textNodes.length; i++) {
      text += textNodes[i].textContent || '';
    }
    return text.trim();
  }

  /**
   * 检测选项标签和内容对
   */
  private isOptionLabelAndContentPair(text1: string, text2: string): boolean {
    // 第一个是选项标签：" A、 ", " B、 ", " C、 ", " D、 " 等
    const isLabel = /^\s*[A-Z][、.]\s*$/.test(text1);

    // 第二个是选项内容（不为空且不是另一个选项标签）
    const isContent = text2.length > 0 && !/^\s*[A-Z][、.]\s*$/.test(text2);

    return isLabel && isContent;
  }

  /**
   * 检测题目序号和内容对
   */
  private isQuestionNumberAndContentPair(text1: string, text2: string): boolean {
    // 第一个是题目序号：" 1. ", " 2、 " 等
    const isNumber = /^\s*\d+[、.]\s*$/.test(text1);

    // 第二个是题目内容（不为空且不是另一个序号）
    const isContent = text2.length > 0 && !/^\s*\d+[、.]\s*$/.test(text2);

    return isNumber && isContent;
  }

  /**
   * 检测是否都是短文本对
   */
  private areShortTextPair(text1: string, text2: string): boolean {
    // 都是短文本（长度小于30个字符）且都不为空
    const isShort1 = text1.length > 0 && text1.length < 30;
    const isShort2 = text2.length > 0 && text2.length < 30;

    // 都不包含句号、问号、感叹号等句子结束标记
    const noEndPunctuation1 = !/[。？！]/.test(text1);
    const noEndPunctuation2 = !/[。？！]/.test(text2);

    return isShort1 && isShort2 && noEndPunctuation1 && noEndPunctuation2;
  }

  // 扩展点：添加其他特殊样式处理方法
  // private hasSpecialFonts(): boolean { return false; }
  // private processSpecialFonts(xmlDoc: Document): Promise<boolean> { return Promise.resolve(false); }
  // private hasSpecialColors(): boolean { return false; }
  // private processSpecialColors(xmlDoc: Document): Promise<boolean> { return Promise.resolve(false); }
}

/**
 * 便捷函数：对Word文档应用XML补丁
 * 简洁接口：自动检测并处理特殊样式
 */
export async function patchWordDocument(
  docxBuffer: Buffer,
  originalHtml: string
): Promise<Buffer> {
  const patcher = new WordXmlPatcherV2(docxBuffer, originalHtml);
  return await patcher.applyPatches();
}
